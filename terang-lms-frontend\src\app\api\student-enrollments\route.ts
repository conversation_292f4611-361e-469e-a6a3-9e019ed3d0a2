import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { studentEnrollments, courses } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

// GET /api/student-enrollments - Get student's enrolled courses
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    // Get student's enrollments with course details
    const enrollments = await db
      .select({
        id: studentEnrollments.id,
        courseId: studentEnrollments.courseId,
        enrolledAt: studentEnrollments.enrolledAt,
        completedAt: studentEnrollments.completedAt,
        finalScore: studentEnrollments.finalScore,
        courseName: courses.name,
        courseDescription: courses.description,
        courseCode: courses.courseCode,
        courseType: courses.type
      })
      .from(studentEnrollments)
      .innerJoin(courses, eq(studentEnrollments.courseId, courses.id))
      .where(eq(studentEnrollments.studentId, parseInt(studentId)));

    return NextResponse.json({
      success: true,
      data: enrollments
    });

  } catch (error) {
    console.error('Error fetching student enrollments:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/student-enrollments - Enroll student in course
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, courseId, courseCode } = body;

    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    let targetCourseId = courseId;

    // If courseCode is provided, find the course
    if (courseCode && !courseId) {
      const course = await db
        .select({ id: courses.id, name: courses.name })
        .from(courses)
        .where(
          or(
            eq(courses.courseCode, courseCode),
            eq(courses.id, parseInt(courseCode) || 0)
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { success: false, error: 'Course not found' },
          { status: 404 }
        );
      }

      targetCourseId = course[0].id;
    }

    if (!targetCourseId) {
      return NextResponse.json(
        { success: false, error: 'Course ID or Course Code is required' },
        { status: 400 }
      );
    }

    // Check if already enrolled
    const existingEnrollment = await db
      .select()
      .from(studentEnrollments)
      .where(
        and(
          eq(studentEnrollments.studentId, parseInt(studentId)),
          eq(studentEnrollments.courseId, targetCourseId)
        )
      )
      .limit(1);

    if (existingEnrollment.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Already enrolled in this course' },
        { status: 409 }
      );
    }

    // Get course details for response
    const courseDetails = await db
      .select({
        id: courses.id,
        name: courses.name,
        description: courses.description,
        courseCode: courses.courseCode
      })
      .from(courses)
      .where(eq(courses.id, targetCourseId))
      .limit(1);

    if (courseDetails.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      );
    }

    // Create enrollment
    const newEnrollment = await db
      .insert(studentEnrollments)
      .values({
        studentId: parseInt(studentId),
        courseId: targetCourseId
      })
      .returning();

    return NextResponse.json({
      success: true,
      data: {
        enrollment: newEnrollment[0],
        course: courseDetails[0]
      },
      message: `Successfully enrolled in ${courseDetails[0].name}`
    });

  } catch (error) {
    console.error('Error creating student enrollment:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/student-enrollments - Update enrollment (e.g., mark as completed)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, courseId, completedAt, finalScore } = body;

    if (!studentId || !courseId) {
      return NextResponse.json(
        { success: false, error: 'Student ID and Course ID are required' },
        { status: 400 }
      );
    }

    // Find existing enrollment
    const existingEnrollment = await db
      .select()
      .from(studentEnrollments)
      .where(
        and(
          eq(studentEnrollments.studentId, parseInt(studentId)),
          eq(studentEnrollments.courseId, parseInt(courseId))
        )
      )
      .limit(1);

    if (existingEnrollment.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Enrollment not found' },
        { status: 404 }
      );
    }

    // Update enrollment
    const updatedEnrollment = await db
      .update(studentEnrollments)
      .set({
        completedAt: completedAt ? new Date(completedAt) : null,
        finalScore: finalScore ? finalScore.toString() : null,
        certificateGenerated: completedAt ? true : false
      })
      .where(
        and(
          eq(studentEnrollments.studentId, parseInt(studentId)),
          eq(studentEnrollments.courseId, parseInt(courseId))
        )
      )
      .returning();

    return NextResponse.json({
      success: true,
      data: updatedEnrollment[0],
      message: 'Enrollment updated successfully'
    });

  } catch (error) {
    console.error('Error updating student enrollment:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
