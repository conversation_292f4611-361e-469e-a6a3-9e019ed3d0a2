import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { courseEnrollments, studentEnrollments, courses, classes, users } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

// GET /api/enrollments - Get enrollments (course-to-class or student-to-course)
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type'); // 'course' or 'student'
    const teacherId = searchParams.get('teacherId');
    const courseId = searchParams.get('courseId');
    const classId = searchParams.get('classId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    if (type === 'course') {
      // Get course enrollments (courses assigned to classes)
      let whereCondition;
      
      if (courseId) {
        // Get classes enrolled in a specific course
        whereCondition = eq(courseEnrollments.courseId, parseInt(courseId));
      } else if (classId) {
        // Get course enrollments for a specific class
        const teacherCourses = await db
          .select({ id: courses.id })
          .from(courses)
          .where(eq(courses.teacherId, parseInt(teacherId)));
        
        const courseIds = teacherCourses.map(c => c.id);
        if (courseIds.length === 0) {
          return NextResponse.json({ enrollments: [] });
        }
        
        whereCondition = and(
          eq(courseEnrollments.classId, parseInt(classId)),
          or(...courseIds.map(id => eq(courseEnrollments.courseId, id)))
        );
      } else {
        // Get all course enrollments for teacher's courses
        const teacherCourses = await db
          .select({ id: courses.id })
          .from(courses)
          .where(eq(courses.teacherId, parseInt(teacherId)));
        
        const courseIds = teacherCourses.map(c => c.id);
        if (courseIds.length === 0) {
          return NextResponse.json({ enrollments: [] });
        }
        
        whereCondition = or(...courseIds.map(id => eq(courseEnrollments.courseId, id)));
      }

      const enrollments = await db
        .select({
          id: courseEnrollments.id,
          courseId: courseEnrollments.courseId,
          classId: courseEnrollments.classId,
          enrolledAt: courseEnrollments.enrolledAt,
          courseName: courses.name,
          courseCode: courses.courseCode,
          className: classes.name
        })
        .from(courseEnrollments)
        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))
        .leftJoin(classes, eq(courseEnrollments.classId, classes.id))
        .where(whereCondition);

      return NextResponse.json({ enrollments });
    } else if (type === 'student') {
      // Get student enrollments (students enrolled in courses)
      let whereCondition;
      
      if (courseId) {
        // Get students enrolled in a specific course
        whereCondition = eq(studentEnrollments.courseId, parseInt(courseId));
      } else {
        // Get all student enrollments for teacher's courses
        const teacherCourses = await db
          .select({ id: courses.id })
          .from(courses)
          .where(eq(courses.teacherId, parseInt(teacherId)));
        
        const courseIds = teacherCourses.map(c => c.id);
        if (courseIds.length === 0) {
          return NextResponse.json({ enrollments: [] });
        }
        
        whereCondition = or(...courseIds.map(id => eq(studentEnrollments.courseId, id)));
      }

      const enrollments = await db
        .select({
          id: studentEnrollments.id,
          studentId: studentEnrollments.studentId,
          courseId: studentEnrollments.courseId,
          enrolledAt: studentEnrollments.enrolledAt,
          completedAt: studentEnrollments.completedAt,
          finalScore: studentEnrollments.finalScore,
          certificateGenerated: studentEnrollments.certificateGenerated,
          studentName: users.name,
          studentEmail: users.email,
          courseName: courses.name,
          courseCode: courses.courseCode
        })
        .from(studentEnrollments)
        .leftJoin(users, eq(studentEnrollments.studentId, users.id))
        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
        .where(whereCondition);

      return NextResponse.json({ enrollments });
    } else {
      return NextResponse.json({ error: 'Type parameter required (course or student)' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error fetching enrollments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/enrollments - Create new enrollment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      type, // 'course' or 'student' or 'course_code'
      courseId,
      classId,
      studentId,
      courseCode,
      teacherId
    } = body;

    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    if (type === 'course') {
      // Enroll a course to a class
      if (!courseId || !classId) {
        return NextResponse.json(
          { error: 'Course ID and Class ID are required' },
          { status: 400 }
        );
      }

      // Verify teacher owns the course
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, courseId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or not authorized' },
          { status: 403 }
        );
      }

      // Verify class exists and belongs to same institution
      const classData = await db
        .select()
        .from(classes)
        .where(
          and(
            eq(classes.id, classId),
            eq(classes.institutionId, course[0].institutionId)
          )
        )
        .limit(1);

      if (classData.length === 0) {
        return NextResponse.json(
          { error: 'Class not found or not in same institution' },
          { status: 404 }
        );
      }

      // Check if enrollment already exists
      const existingEnrollment = await db
        .select()
        .from(courseEnrollments)
        .where(
          and(
            eq(courseEnrollments.courseId, courseId),
            eq(courseEnrollments.classId, classId)
          )
        )
        .limit(1);

      if (existingEnrollment.length > 0) {
        return NextResponse.json(
          { error: 'Course already enrolled to this class' },
          { status: 400 }
        );
      }

      // Create course enrollment
      const newEnrollment = await db
        .insert(courseEnrollments)
        .values({
          courseId,
          classId
        })
        .returning();

      return NextResponse.json(
        { enrollment: newEnrollment[0], message: 'Course enrolled to class successfully' },
        { status: 201 }
      );
    } else if (type === 'student') {
      // Enroll a student to a course
      if (!studentId || !courseId) {
        return NextResponse.json(
          { error: 'Student ID and Course ID are required' },
          { status: 400 }
        );
      }

      // Verify course exists and teacher owns it
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, courseId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or not authorized' },
          { status: 403 }
        );
      }

      // Verify student exists and belongs to same institution
      const student = await db
        .select()
        .from(users)
        .where(
          and(
            eq(users.id, studentId),
            eq(users.role, 'student'),
            eq(users.institutionId, course[0].institutionId)
          )
        )
        .limit(1);

      if (student.length === 0) {
        return NextResponse.json(
          { error: 'Student not found or not in same institution' },
          { status: 404 }
        );
      }

      // Check if enrollment already exists
      const existingEnrollment = await db
        .select()
        .from(studentEnrollments)
        .where(
          and(
            eq(studentEnrollments.studentId, studentId),
            eq(studentEnrollments.courseId, courseId)
          )
        )
        .limit(1);

      if (existingEnrollment.length > 0) {
        return NextResponse.json(
          { error: 'Student already enrolled in this course' },
          { status: 400 }
        );
      }

      // Create student enrollment
      const newEnrollment = await db
        .insert(studentEnrollments)
        .values({
          studentId,
          courseId
        })
        .returning();

      return NextResponse.json(
        { enrollment: newEnrollment[0], message: 'Student enrolled in course successfully' },
        { status: 201 }
      );
    } else if (type === 'course_code') {
      // Enroll student using course code (for student self-enrollment)
      if (!studentId || !courseCode) {
        return NextResponse.json(
          { error: 'Student ID and Course Code are required' },
          { status: 400 }
        );
      }

      // Find course by code or ID (more flexible)
      const course = await db
        .select()
        .from(courses)
        .where(
          or(
            eq(courses.courseCode, courseCode),
            eq(courses.id, parseInt(courseCode) || 0)
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Invalid course code' },
          { status: 404 }
        );
      }

      // For demo purposes, skip institution validation
      // In production, you should validate student exists and belongs to same institution

      // Check if enrollment already exists
      const existingEnrollment = await db
        .select()
        .from(studentEnrollments)
        .where(
          and(
            eq(studentEnrollments.studentId, studentId),
            eq(studentEnrollments.courseId, course[0].id)
          )
        )
        .limit(1);

      if (existingEnrollment.length > 0) {
        return NextResponse.json(
          { error: 'Already enrolled in this course' },
          { status: 400 }
        );
      }

      // Create student enrollment
      const newEnrollment = await db
        .insert(studentEnrollments)
        .values({
          studentId,
          courseId: course[0].id
        })
        .returning();

      return NextResponse.json(
        { enrollment: newEnrollment[0], course: course[0], message: 'Successfully enrolled in course' },
        { status: 201 }
      );
    } else {
      return NextResponse.json(
        { error: 'Invalid enrollment type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating enrollment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}