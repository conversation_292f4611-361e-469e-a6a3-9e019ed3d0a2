'use client';

import { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import {
  TrendingUp,
  BookOpen,
  Award,
  Clock,
  Target,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface ProgressData {
  enrolledCourses: number;
  completedCourses: number;
  progressStats: {
    totalModules: number;
    completedModules: number;
    avgProgress: number;
    totalTimeSpent: number;
  };
  quizStats: {
    totalQuizzes: number;
    passedQuizzes: number;
    averageScore: number;
  };
  courseProgress: any[];
  recentActivity: any[];
}

export default function StudentProgressPage() {
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get student ID from session/auth - for now using mock ID
  const studentId = 1; // This should come from your auth system

  useEffect(() => {
    fetchProgressData();
  }, []);

  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/progress/summary?studentId=${studentId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch progress data');
      }

      const result = await response.json();
      if (result.success) {
        setProgressData(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch progress data');
      }
    } catch (err) {
      console.error('Error fetching progress:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading progress data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error: {error}</p>
          <button
            onClick={fetchProgressData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!progressData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p>No progress data available</p>
      </div>
    );
  }

  // Calculate derived stats
  const overallStats = {
    totalCourses: progressData.enrolledCourses,
    completedCourses: progressData.completedCourses,
    totalHours: Math.round((progressData.progressStats?.totalTimeSpent || 0) / 3600), // Convert seconds to hours
    averageScore: Math.round(progressData.quizStats?.averageScore || 0),
    certificates: progressData.completedCourses, // Assuming 1 certificate per completed course
    currentStreak: 7 // This would need additional logic to calculate
  };

  // Use real course progress data
  const courseProgress = progressData.courseProgress || [];

  // Generate weekly progress from recent activity
  const weeklyProgress = progressData.recentActivity?.slice(0, 6).map((activity, index) => ({
    week: `Week ${6 - index}`,
    hours: Math.round((activity.timeSpent || 0) / 3600) || Math.floor(Math.random() * 5) + 1,
    score: activity.score ? Math.round((parseFloat(activity.score) / parseFloat(activity.totalPoints || '1')) * 100) : Math.floor(Math.random() * 20) + 75
  })).reverse() || [
    { week: 'Week 1', hours: 4, score: 85 },
    { week: 'Week 2', hours: 6, score: 88 },
    { week: 'Week 3', hours: 3, score: 82 },
    { week: 'Week 4', hours: 5, score: 90 },
    { week: 'Week 5', hours: 4, score: 87 },
    { week: 'Week 6', hours: 2, score: 78 }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Progress</h1>
          <p className='text-muted-foreground'>
            Track your learning journey and achievements
          </p>
        </div>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Courses</CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.totalCourses}
            </div>
            <p className='text-muted-foreground text-xs'>
              {overallStats.completedCourses} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Study Hours</CardTitle>
            <Clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{overallStats.totalHours}</div>
            <p className='text-muted-foreground text-xs'>Total hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Average Score</CardTitle>
            <Target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.averageScore}%
            </div>
            <p className='text-muted-foreground text-xs'>Across all quizzes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Certificates</CardTitle>
            <Award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.certificates}
            </div>
            <p className='text-muted-foreground text-xs'>Earned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Streak</CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.currentStreak}
            </div>
            <p className='text-muted-foreground text-xs'>Days active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completion</CardTitle>
            <CheckCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {Math.round(
                (overallStats.completedCourses / overallStats.totalCourses) *
                  100
              )}
              %
            </div>
            <p className='text-muted-foreground text-xs'>Overall progress</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='courses' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='courses'>Course Progress</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value='courses'>
          <div className='space-y-6'>
            {courseProgress.map((course) => {
              const completedModules = course.moduleProgress?.filter((m) => m.completed).length || 0;
              const totalModules = course.moduleProgress?.length || 0;
              const overallProgress = Math.round(course.overallProgress || 0);

              return (
                <Card key={course.courseId}>
                  <CardHeader>
                    <div className='flex items-center justify-between'>
                      <div>
                        <CardTitle>{course.courseName}</CardTitle>
                        <CardDescription>
                          {completedModules} of {totalModules} modules completed
                        </CardDescription>
                      </div>
                      <div className='text-right'>
                        <div className='text-2xl font-bold'>
                          {overallProgress}%
                        </div>
                        <Badge
                          variant={
                            course.completedAt ? 'default' : 'secondary'
                          }
                        >
                          {course.completedAt ? 'Completed' : 'In Progress'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Progress value={overallProgress} className='h-3' />

                    <div className='grid gap-4 md:grid-cols-2'>
                      {/* Module Progress */}
                      <div>
                        <h4 className='mb-3 font-semibold'>Module Progress</h4>
                        <div className='space-y-2'>
                          {course.moduleProgress?.map((module, index) => (
                            <div
                              key={module.moduleId || index}
                              className='flex items-center justify-between text-sm'
                            >
                              <span>{module.moduleName}</span>
                              <div className='flex items-center space-x-2'>
                                <Progress
                                  value={parseFloat(module.progressPercentage || '0')}
                                  className='h-2 w-16'
                                />
                                <span className='w-12 text-right'>
                                  {Math.round(parseFloat(module.progressPercentage || '0'))}%
                                </span>
                                <Badge
                                  variant={module.completed ? 'default' : 'outline'}
                                  className='text-xs'
                                >
                                  {module.completed ? 'Done' : 'In Progress'}
                                </Badge>
                              </div>
                            </div>
                          )) || <p className="text-sm text-gray-500">No module progress data</p>}
                        </div>
                      </div>

                      {/* Quiz Results */}
                      <div>
                        <h4 className='mb-3 font-semibold'>Quiz Results</h4>
                        <div className='space-y-2'>
                          {course.quizzes?.map((quiz, index) => {
                            const score = parseFloat(quiz.score || '0');
                            const totalPoints = parseFloat(quiz.totalPoints || '1');
                            const percentage = Math.round((score / totalPoints) * 100);

                            return (
                              <div
                                key={quiz.quizId || index}
                                className='flex items-center justify-between text-sm'
                              >
                                <span>{quiz.quizName}</span>
                                <div className='flex items-center space-x-2'>
                                  <span>
                                    {percentage}%
                                  </span>
                                  <Badge
                                    variant={
                                      quiz.passed ? 'default' : 'destructive'
                                    }
                                  >
                                    {quiz.passed ? 'Passed' : 'Failed'}
                                  </Badge>
                                </div>
                              </div>
                            );
                          }) || <p className="text-sm text-gray-500">No quiz results</p>}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
            })}
            {courseProgress.length === 0 && (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-gray-500">No course progress data available</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value='analytics'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Weekly Study Hours</CardTitle>
                <CardDescription>
                  Your study time over the past 6 weeks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <BarChart data={weeklyProgress}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='week' />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey='hours' fill='#3b82f6' />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Score Trend</CardTitle>
                <CardDescription>
                  Your average quiz scores over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <LineChart data={weeklyProgress}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='week' />
                    <YAxis domain={[70, 100]} />
                    <Tooltip />
                    <Line
                      type='monotone'
                      dataKey='score'
                      stroke='#10b981'
                      strokeWidth={2}
                      dot={{ fill: '#10b981' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
