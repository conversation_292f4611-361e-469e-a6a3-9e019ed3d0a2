import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { quizAttempts, quizzes, courses } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// PUT /api/quiz-attempts/[id]/review - Review and update quiz attempt score
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const attemptId = parseInt(id);
    
    if (isNaN(attemptId)) {
      return NextResponse.json({ error: 'Invalid attempt ID' }, { status: 400 });
    }

    const body = await request.json();
    const { 
      teacherId,
      reviewedAnswers, // Array of reviewed answers with updated scores
      teacherFeedback,
      finalScore,
      finalTotalPoints,
      passed
    } = body;

    if (!teacherId || !reviewedAnswers) {
      return NextResponse.json(
        { error: 'Teacher ID and reviewed answers are required' }, 
        { status: 400 }
      );
    }

    // Get the quiz attempt
    const attempt = await db
      .select({
        id: quizAttempts.id,
        studentId: quizAttempts.studentId,
        quizId: quizAttempts.quizId,
        answers: quizAttempts.answers,
        isReviewed: quizAttempts.isReviewed
      })
      .from(quizAttempts)
      .where(eq(quizAttempts.id, attemptId))
      .limit(1);

    if (attempt.length === 0) {
      return NextResponse.json(
        { error: 'Quiz attempt not found' },
        { status: 404 }
      );
    }

    // Verify teacher has permission to review this quiz
    const quizInfo = await db
      .select({
        id: quizzes.id,
        courseId: quizzes.courseId,
        teacherId: courses.teacherId
      })
      .from(quizzes)
      .leftJoin(courses, eq(quizzes.courseId, courses.id))
      .where(eq(quizzes.id, attempt[0].quizId))
      .limit(1);

    if (quizInfo.length === 0 || quizInfo[0].teacherId !== teacherId) {
      return NextResponse.json(
        { error: 'Unauthorized to review this quiz' },
        { status: 403 }
      );
    }

    // Update the quiz attempt with reviewed scores
    await db
      .update(quizAttempts)
      .set({
        score: finalScore.toString(),
        totalPoints: finalTotalPoints.toString(),
        passed,
        answers: reviewedAnswers,
        isReviewed: true,
        teacherFeedback,
        updatedAt: new Date()
      })
      .where(eq(quizAttempts.id, attemptId));

    return NextResponse.json({
      success: true,
      message: 'Quiz attempt reviewed successfully',
      data: {
        attemptId,
        finalScore,
        finalTotalPoints,
        passed,
        percentage: Math.round((finalScore / finalTotalPoints) * 100)
      }
    });

  } catch (error) {
    console.error('Error reviewing quiz attempt:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/quiz-attempts/[id]/review - Get quiz attempt details for review
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const attemptId = parseInt(id);
    
    if (isNaN(attemptId)) {
      return NextResponse.json({ error: 'Invalid attempt ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    if (!teacherId) {
      return NextResponse.json(
        { error: 'Teacher ID is required' }, 
        { status: 400 }
      );
    }

    // Get the quiz attempt with full details
    const attemptDetails = await db
      .select({
        id: quizAttempts.id,
        studentId: quizAttempts.studentId,
        quizId: quizAttempts.quizId,
        score: quizAttempts.score,
        totalPoints: quizAttempts.totalPoints,
        passed: quizAttempts.passed,
        startedAt: quizAttempts.startedAt,
        completedAt: quizAttempts.completedAt,
        answers: quizAttempts.answers,
        timeSpent: quizAttempts.timeSpent,
        attemptNumber: quizAttempts.attemptNumber,
        isReviewed: quizAttempts.isReviewed,
        teacherFeedback: quizAttempts.teacherFeedback,
        quizName: quizzes.name,
        quizType: quizzes.quizType,
        minimumScore: quizzes.minimumScore,
        courseId: quizzes.courseId,
        teacherId: courses.teacherId
      })
      .from(quizAttempts)
      .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
      .leftJoin(courses, eq(quizzes.courseId, courses.id))
      .where(eq(quizAttempts.id, attemptId))
      .limit(1);

    if (attemptDetails.length === 0) {
      return NextResponse.json(
        { error: 'Quiz attempt not found' },
        { status: 404 }
      );
    }

    // Verify teacher has permission
    if (attemptDetails[0].teacherId !== parseInt(teacherId)) {
      return NextResponse.json(
        { error: 'Unauthorized to review this quiz' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: attemptDetails[0]
    });

  } catch (error) {
    console.error('Error fetching quiz attempt for review:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
