/**
 * Progress Tracking Utilities
 * Handles automatic progress tracking for students as they complete content, chapters, modules, and quizzes
 */

export interface ProgressUpdate {
  studentId: number;
  courseId: number;
  moduleId?: number;
  chapterId?: number;
  contentId?: string;
  completed?: boolean;
  progressPercentage?: number;
  timeSpent?: number;
  type: 'content' | 'chapter' | 'module' | 'course';
}

export interface ContentProgressData {
  contentId: string;
  chapterId: number;
  timeSpent: number;
  completed: boolean;
}

export interface ChapterProgressData {
  chapterId: number;
  moduleId: number;
  courseId: number;
  timeSpent: number;
  completed: boolean;
  progressPercentage: number;
}

export interface ModuleProgressData {
  moduleId: number;
  courseId: number;
  timeSpent: number;
  completed: boolean;
  progressPercentage: number;
}

/**
 * Progress Tracker Class
 * Manages automatic progress tracking and updates
 */
export class ProgressTracker {
  private static instance: ProgressTracker;
  private progressQueue: ProgressUpdate[] = [];
  private isProcessing = false;
  private batchTimeout: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): ProgressTracker {
    if (!ProgressTracker.instance) {
      ProgressTracker.instance = new ProgressTracker();
    }
    return ProgressTracker.instance;
  }

  /**
   * Track content completion
   */
  public async trackContentProgress(data: ContentProgressData): Promise<void> {
    const update: ProgressUpdate = {
      studentId: this.getCurrentStudentId(),
      courseId: await this.getCourseIdFromChapter(data.chapterId),
      chapterId: data.chapterId,
      contentId: data.contentId,
      completed: data.completed,
      timeSpent: data.timeSpent,
      type: 'content'
    };

    this.queueUpdate(update);

    // Check if chapter should be marked as completed
    if (data.completed) {
      await this.checkChapterCompletion(data.chapterId);
    }
  }

  /**
   * Track chapter completion
   */
  public async trackChapterProgress(data: ChapterProgressData): Promise<void> {
    const update: ProgressUpdate = {
      studentId: this.getCurrentStudentId(),
      courseId: data.courseId,
      moduleId: data.moduleId,
      chapterId: data.chapterId,
      completed: data.completed,
      progressPercentage: data.progressPercentage,
      timeSpent: data.timeSpent,
      type: 'chapter'
    };

    this.queueUpdate(update);

    // Check if module should be marked as completed
    if (data.completed) {
      await this.checkModuleCompletion(data.moduleId);
    }
  }

  /**
   * Track module completion
   */
  public async trackModuleProgress(data: ModuleProgressData): Promise<void> {
    const update: ProgressUpdate = {
      studentId: this.getCurrentStudentId(),
      courseId: data.courseId,
      moduleId: data.moduleId,
      completed: data.completed,
      progressPercentage: data.progressPercentage,
      timeSpent: data.timeSpent,
      type: 'module'
    };

    this.queueUpdate(update);

    // Check if course should be marked as completed
    if (data.completed) {
      await this.checkCourseCompletion(data.courseId);
    }
  }

  /**
   * Track quiz completion and update related progress
   */
  public async trackQuizCompletion(
    quizId: number,
    passed: boolean,
    score: number,
    chapterId?: number,
    moduleId?: number,
    courseId?: number
  ): Promise<void> {
    if (passed) {
      if (chapterId) {
        // Mark chapter as completed if quiz passed
        const courseIdResolved = courseId || await this.getCourseIdFromChapter(chapterId);
        const moduleIdResolved = moduleId || await this.getModuleIdFromChapter(chapterId);
        
        await this.trackChapterProgress({
          chapterId,
          moduleId: moduleIdResolved,
          courseId: courseIdResolved,
          timeSpent: 0, // Quiz time is tracked separately
          completed: true,
          progressPercentage: 100
        });
      } else if (moduleId) {
        // Mark module as completed if module quiz passed
        const courseIdResolved = courseId || await this.getCourseIdFromModule(moduleId);
        
        await this.trackModuleProgress({
          moduleId,
          courseId: courseIdResolved,
          timeSpent: 0,
          completed: true,
          progressPercentage: 100
        });
      }
    }
  }

  /**
   * Queue progress update for batch processing
   */
  private queueUpdate(update: ProgressUpdate): void {
    this.progressQueue.push(update);
    this.scheduleBatchProcess();
  }

  /**
   * Schedule batch processing of progress updates
   */
  private scheduleBatchProcess(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processBatch();
    }, 2000); // Process batch every 2 seconds
  }

  /**
   * Process queued progress updates
   */
  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.progressQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const updates = [...this.progressQueue];
    this.progressQueue = [];

    try {
      // Group updates by type for efficient processing
      const groupedUpdates = this.groupUpdatesByType(updates);

      for (const [type, typeUpdates] of Object.entries(groupedUpdates)) {
        await this.processUpdatesByType(type as ProgressUpdate['type'], typeUpdates);
      }
    } catch (error) {
      console.error('Error processing progress batch:', error);
      // Re-queue failed updates
      this.progressQueue.unshift(...updates);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Group updates by type for efficient processing
   */
  private groupUpdatesByType(updates: ProgressUpdate[]): Record<string, ProgressUpdate[]> {
    return updates.reduce((groups, update) => {
      if (!groups[update.type]) {
        groups[update.type] = [];
      }
      groups[update.type].push(update);
      return groups;
    }, {} as Record<string, ProgressUpdate[]>);
  }

  /**
   * Process updates by type
   */
  private async processUpdatesByType(type: ProgressUpdate['type'], updates: ProgressUpdate[]): Promise<void> {
    for (const update of updates) {
      try {
        await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(update),
        });
      } catch (error) {
        console.error(`Error updating ${type} progress:`, error);
      }
    }
  }

  /**
   * Check if chapter should be marked as completed based on content completion
   */
  private async checkChapterCompletion(chapterId: number): Promise<void> {
    try {
      const response = await fetch(`/api/chapters/${chapterId}/content-progress?studentId=${this.getCurrentStudentId()}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.allContentCompleted) {
          // Chapter is completed, update progress
          await this.trackChapterProgress({
            chapterId,
            moduleId: result.data.moduleId,
            courseId: result.data.courseId,
            timeSpent: 0,
            completed: true,
            progressPercentage: 100
          });
        }
      }
    } catch (error) {
      console.error('Error checking chapter completion:', error);
    }
  }

  /**
   * Check if module should be marked as completed based on chapter completion
   */
  private async checkModuleCompletion(moduleId: number): Promise<void> {
    try {
      const response = await fetch(`/api/modules/${moduleId}/chapter-progress?studentId=${this.getCurrentStudentId()}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.allChaptersCompleted) {
          // Module is completed, update progress
          await this.trackModuleProgress({
            moduleId,
            courseId: result.data.courseId,
            timeSpent: 0,
            completed: true,
            progressPercentage: 100
          });
        }
      }
    } catch (error) {
      console.error('Error checking module completion:', error);
    }
  }

  /**
   * Check if course should be marked as completed based on module completion
   */
  private async checkCourseCompletion(courseId: number): Promise<void> {
    try {
      const response = await fetch(`/api/courses/${courseId}/module-progress?studentId=${this.getCurrentStudentId()}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.allModulesCompleted) {
          // Course is completed, update enrollment
          await fetch('/api/student-enrollments', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              studentId: this.getCurrentStudentId(),
              courseId,
              completedAt: new Date().toISOString(),
              finalScore: result.data.averageScore
            }),
          });
        }
      }
    } catch (error) {
      console.error('Error checking course completion:', error);
    }
  }

  /**
   * Helper methods to get related IDs
   */
  private async getCourseIdFromChapter(chapterId: number): Promise<number> {
    // This should be implemented to fetch course ID from chapter
    // For now, return a placeholder
    return 1;
  }

  private async getModuleIdFromChapter(chapterId: number): Promise<number> {
    // This should be implemented to fetch module ID from chapter
    // For now, return a placeholder
    return 1;
  }

  private async getCourseIdFromModule(moduleId: number): Promise<number> {
    // This should be implemented to fetch course ID from module
    // For now, return a placeholder
    return 1;
  }

  /**
   * Get current student ID (should be implemented based on your auth system)
   */
  private getCurrentStudentId(): number {
    // This should be implemented to get the current student ID from your auth system
    // For now, return a placeholder
    return 1;
  }
}

/**
 * Convenience functions for easy access
 */
export const progressTracker = ProgressTracker.getInstance();

export const trackContentProgress = (data: ContentProgressData) => 
  progressTracker.trackContentProgress(data);

export const trackChapterProgress = (data: ChapterProgressData) => 
  progressTracker.trackChapterProgress(data);

export const trackModuleProgress = (data: ModuleProgressData) => 
  progressTracker.trackModuleProgress(data);

export const trackQuizCompletion = (
  quizId: number,
  passed: boolean,
  score: number,
  chapterId?: number,
  moduleId?: number,
  courseId?: number
) => progressTracker.trackQuizCompletion(quizId, passed, score, chapterId, moduleId, courseId);
