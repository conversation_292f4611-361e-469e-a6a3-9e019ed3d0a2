import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { 
  studentProgress, 
  studentEnrollments,
  quizAttempts,
  users,
  courses,
  modules,
  chapters,
  quizzes
} from '@/lib/db/schema';
import { eq, and, avg, count, sum, desc } from 'drizzle-orm';

// GET /api/progress/summary - Get comprehensive progress summary
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');
    const courseId = searchParams.get('courseId');
    const teacherId = searchParams.get('teacherId');
    
    if (!studentId && !teacherId) {
      return NextResponse.json(
        { error: 'Either Student ID or Teacher ID is required' }, 
        { status: 400 }
      );
    }

    if (studentId) {
      // Get student's progress summary
      return await getStudentProgressSummary(parseInt(studentId), courseId ? parseInt(courseId) : null);
    } else if (teacherId) {
      // Get teacher's students progress summary
      return await getTeacherStudentsProgressSummary(parseInt(teacherId), courseId ? parseInt(courseId) : null);
    }

  } catch (error) {
    console.error('Error fetching progress summary:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getStudentProgressSummary(studentId: number, courseId: number | null) {
  try {
    // Get enrolled courses
    let enrollmentQuery = db
      .select({
        courseId: studentEnrollments.courseId,
        courseName: courses.name,
        courseType: courses.type,
        enrolledAt: studentEnrollments.enrolledAt,
        completedAt: studentEnrollments.completedAt,
        finalScore: studentEnrollments.finalScore,
        certificateGenerated: studentEnrollments.certificateGenerated
      })
      .from(studentEnrollments)
      .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
      .where(eq(studentEnrollments.studentId, studentId));

    if (courseId) {
      enrollmentQuery = enrollmentQuery.where(eq(studentEnrollments.courseId, courseId));
    }

    const enrolledCourses = await enrollmentQuery;

    // Get overall progress statistics
    const progressStats = await db
      .select({
        totalModules: count(studentProgress.id),
        completedModules: sum(studentProgress.completed),
        avgProgress: avg(studentProgress.progressPercentage),
        totalTimeSpent: sum(studentProgress.timeSpent)
      })
      .from(studentProgress)
      .where(
        and(
          eq(studentProgress.studentId, studentId),
          courseId ? eq(studentProgress.courseId, courseId) : undefined
        )
      );

    // Get quiz performance
    let quizQuery = db
      .select({
        quizId: quizAttempts.quizId,
        quizName: quizzes.name,
        quizType: quizzes.quizType,
        score: quizAttempts.score,
        totalPoints: quizAttempts.totalPoints,
        passed: quizAttempts.passed,
        completedAt: quizAttempts.completedAt,
        attemptNumber: quizAttempts.attemptNumber,
        courseName: courses.name
      })
      .from(quizAttempts)
      .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
      .leftJoin(courses, eq(quizzes.courseId, courses.id))
      .where(eq(quizAttempts.studentId, studentId))
      .orderBy(desc(quizAttempts.completedAt));

    if (courseId) {
      quizQuery = quizQuery.where(eq(courses.id, courseId));
    }

    const quizHistory = await quizQuery;

    // Calculate quiz statistics
    const quizStats = {
      totalQuizzes: quizHistory.length,
      passedQuizzes: quizHistory.filter(q => q.passed).length,
      averageScore: quizHistory.length > 0 
        ? quizHistory.reduce((sum, quiz) => {
            const score = parseFloat(quiz.score || '0');
            const total = parseFloat(quiz.totalPoints || '1');
            return sum + ((score / total) * 100);
          }, 0) / quizHistory.length
        : 0
    };

    // Get detailed course progress
    const courseProgress = [];
    for (const course of enrolledCourses) {
      const moduleProgress = await db
        .select({
          moduleId: studentProgress.moduleId,
          moduleName: modules.name,
          completed: studentProgress.completed,
          progressPercentage: studentProgress.progressPercentage,
          completedAt: studentProgress.completedAt,
          timeSpent: studentProgress.timeSpent
        })
        .from(studentProgress)
        .leftJoin(modules, eq(studentProgress.moduleId, modules.id))
        .where(
          and(
            eq(studentProgress.studentId, studentId),
            eq(studentProgress.courseId, course.courseId),
            eq(studentProgress.moduleId, modules.id) // Only module-level progress
          )
        );

      const chapterProgress = await db
        .select({
          chapterId: studentProgress.chapterId,
          chapterName: chapters.name,
          moduleId: chapters.moduleId,
          completed: studentProgress.completed,
          progressPercentage: studentProgress.progressPercentage,
          completedAt: studentProgress.completedAt
        })
        .from(studentProgress)
        .leftJoin(chapters, eq(studentProgress.chapterId, chapters.id))
        .where(
          and(
            eq(studentProgress.studentId, studentId),
            eq(studentProgress.courseId, course.courseId),
            eq(studentProgress.chapterId, chapters.id) // Only chapter-level progress
          )
        );

      const courseQuizzes = quizHistory.filter(q => q.courseName === course.courseName);

      courseProgress.push({
        ...course,
        moduleProgress,
        chapterProgress,
        quizzes: courseQuizzes,
        overallProgress: moduleProgress.length > 0 
          ? moduleProgress.reduce((sum, m) => sum + parseFloat(m.progressPercentage || '0'), 0) / moduleProgress.length
          : 0
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        student: { id: studentId },
        enrolledCourses: enrolledCourses.length,
        completedCourses: enrolledCourses.filter(c => c.completedAt).length,
        progressStats: progressStats[0],
        quizStats,
        courseProgress,
        recentActivity: quizHistory.slice(0, 10) // Last 10 quiz attempts
      }
    });

  } catch (error) {
    console.error('Error in getStudentProgressSummary:', error);
    throw error;
  }
}

async function getTeacherStudentsProgressSummary(teacherId: number, courseId: number | null) {
  try {
    // Get teacher's courses
    let coursesQuery = db
      .select({
        id: courses.id,
        name: courses.name,
        type: courses.type,
        startDate: courses.startDate,
        endDate: courses.endDate
      })
      .from(courses)
      .where(eq(courses.teacherId, teacherId));

    if (courseId) {
      coursesQuery = coursesQuery.where(eq(courses.id, courseId));
    }

    const teacherCourses = await coursesQuery;

    // Get students enrolled in teacher's courses
    const studentsProgress = [];
    
    for (const course of teacherCourses) {
      const enrolledStudents = await db
        .select({
          studentId: studentEnrollments.studentId,
          studentName: users.name,
          studentEmail: users.email,
          enrolledAt: studentEnrollments.enrolledAt,
          completedAt: studentEnrollments.completedAt,
          finalScore: studentEnrollments.finalScore
        })
        .from(studentEnrollments)
        .leftJoin(users, eq(studentEnrollments.studentId, users.id))
        .where(eq(studentEnrollments.courseId, course.id));

      for (const student of enrolledStudents) {
        // Get student's progress in this course
        const progress = await db
          .select({
            completed: studentProgress.completed,
            progressPercentage: studentProgress.progressPercentage,
            timeSpent: studentProgress.timeSpent,
            lastAccessedAt: studentProgress.lastAccessedAt
          })
          .from(studentProgress)
          .where(
            and(
              eq(studentProgress.studentId, student.studentId),
              eq(studentProgress.courseId, course.id)
            )
          );

        // Get quiz attempts
        const quizAttempts = await db
          .select({
            score: quizAttempts.score,
            totalPoints: quizAttempts.totalPoints,
            passed: quizAttempts.passed,
            isReviewed: quizAttempts.isReviewed
          })
          .from(quizAttempts)
          .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
          .where(
            and(
              eq(quizAttempts.studentId, student.studentId),
              eq(quizzes.courseId, course.id)
            )
          );

        const avgQuizScore = quizAttempts.length > 0
          ? quizAttempts.reduce((sum, quiz) => {
              const score = parseFloat(quiz.score || '0');
              const total = parseFloat(quiz.totalPoints || '1');
              return sum + ((score / total) * 100);
            }, 0) / quizAttempts.length
          : 0;

        const overallProgress = progress.length > 0
          ? progress.reduce((sum, p) => sum + parseFloat(p.progressPercentage || '0'), 0) / progress.length
          : 0;

        studentsProgress.push({
          ...student,
          course: course.name,
          courseId: course.id,
          courseType: course.type,
          overallProgress: Math.round(overallProgress),
          quizAverage: Math.round(avgQuizScore),
          totalQuizzes: quizAttempts.length,
          passedQuizzes: quizAttempts.filter(q => q.passed).length,
          needsReview: quizAttempts.filter(q => !q.isReviewed).length,
          lastActivity: progress.length > 0 
            ? Math.max(...progress.map(p => new Date(p.lastAccessedAt || 0).getTime()))
            : null
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        teacher: { id: teacherId },
        courses: teacherCourses,
        studentsProgress,
        summary: {
          totalStudents: studentsProgress.length,
          totalCourses: teacherCourses.length,
          averageProgress: studentsProgress.length > 0
            ? studentsProgress.reduce((sum, s) => sum + s.overallProgress, 0) / studentsProgress.length
            : 0,
          needsReview: studentsProgress.reduce((sum, s) => sum + s.needsReview, 0)
        }
      }
    });

  } catch (error) {
    console.error('Error in getTeacherStudentsProgressSummary:', error);
    throw error;
  }
}
