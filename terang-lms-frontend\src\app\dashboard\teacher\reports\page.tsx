'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  TrendingUp,
  Users,
  BookOpen,
  Award,
  Download,
  Eye,
  Loader2,
  Edit,
  Save,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

interface ReportsData {
  teacher: { id: number };
  courses: Array<{
    id: number;
    name: string;
    type: string;
    startDate: string | null;
    endDate: string | null;
  }>;
  studentsProgress: Array<{
    studentId: number;
    studentName: string;
    studentEmail: string;
    course: string;
    courseId: number;
    courseType: string;
    overallProgress: number;
    quizAverage: number;
    totalQuizzes: number;
    passedQuizzes: number;
    needsReview: number;
    lastActivity: number | null;
  }>;
  summary: {
    totalStudents: number;
    totalCourses: number;
    averageProgress: number;
    needsReview: number;
  };
}

export default function ReportsPage() {
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [reportsData, setReportsData] = useState<ReportsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedQuizAttempt, setSelectedQuizAttempt] = useState<any>(null);
  const [reviewFeedback, setReviewFeedback] = useState('');

  // Get teacher ID from session/auth - for now using mock ID
  const teacherId = 1; // This should come from your auth system

  useEffect(() => {
    fetchReportsData();
  }, [selectedCourse]);

  const fetchReportsData = async () => {
    try {
      setLoading(true);
      const courseParam = selectedCourse !== 'all' ? `&courseId=${selectedCourse}` : '';
      const response = await fetch(`/api/progress/summary?teacherId=${teacherId}${courseParam}`);

      if (!response.ok) {
        throw new Error('Failed to fetch reports data');
      }

      const result = await response.json();
      if (result.success) {
        setReportsData(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch reports data');
      }
    } catch (err) {
      console.error('Error fetching reports:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading reports data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error: {error}</p>
          <button
            onClick={fetchReportsData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!reportsData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p>No reports data available</p>
      </div>
    );
  }

  // Prepare courses list for dropdown
  const courses = [
    { id: 'all', name: 'All Courses' },
    ...reportsData.courses.map(course => ({
      id: course.id.toString(),
      name: course.name
    }))
  ];

  // Generate progress data from real data
  const progressData = reportsData.courses.map((course, index) => {
    const courseStudents = reportsData.studentsProgress.filter(s => s.courseId === course.id);
    const completed = courseStudents.filter(s => s.overallProgress === 100).length;
    const inProgress = courseStudents.filter(s => s.overallProgress > 0 && s.overallProgress < 100).length;
    const notStarted = courseStudents.filter(s => s.overallProgress === 0).length;

    return {
      name: `Module ${index + 1}`,
      completed: courseStudents.length > 0 ? Math.round((completed / courseStudents.length) * 100) : 0,
      inProgress: courseStudents.length > 0 ? Math.round((inProgress / courseStudents.length) * 100) : 0,
      notStarted: courseStudents.length > 0 ? Math.round((notStarted / courseStudents.length) * 100) : 0
    };
  });

  const completedStudents = reportsData.studentsProgress.filter(s => s.overallProgress === 100).length;
  const inProgressStudents = reportsData.studentsProgress.filter(s => s.overallProgress > 0 && s.overallProgress < 100).length;
  const notStartedStudents = reportsData.studentsProgress.filter(s => s.overallProgress === 0).length;
  const totalStudents = reportsData.studentsProgress.length;

  const completionData = [
    {
      name: 'Completed',
      value: totalStudents > 0 ? Math.round((completedStudents / totalStudents) * 100) : 0,
      color: '#22c55e'
    },
    {
      name: 'In Progress',
      value: totalStudents > 0 ? Math.round((inProgressStudents / totalStudents) * 100) : 0,
      color: '#f59e0b'
    },
    {
      name: 'Not Started',
      value: totalStudents > 0 ? Math.round((notStartedStudents / totalStudents) * 100) : 0,
      color: '#ef4444'
    }
  ];

  // Use real student progress data
  const studentProgress = reportsData.studentsProgress.map(student => ({
    id: student.studentId,
    name: student.studentName,
    email: student.studentEmail,
    course: student.course,
    overallProgress: student.overallProgress,
    quizAverage: student.quizAverage,
    lastActivity: student.lastActivity ? new Date(student.lastActivity).toISOString().split('T')[0] : 'Never',
    status: student.overallProgress === 100 ? 'completed' :
            student.overallProgress >= 70 ? 'on_track' :
            student.overallProgress > 0 ? 'behind' : 'not_started',
    needsReview: student.needsReview,
    courseType: student.courseType
  }));

  // Function to fetch quiz attempts that need review
  const [quizResults, setQuizResults] = useState<any[]>([]);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);

  const fetchQuizAttempts = async () => {
    try {
      setLoadingQuizzes(true);
      const courseParam = selectedCourse !== 'all' ? `&courseId=${selectedCourse}` : '';
      const response = await fetch(`/api/quiz-attempts?teacherId=${teacherId}${courseParam}`);

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setQuizResults(result.data.map((attempt: any) => ({
            id: attempt.id,
            studentName: attempt.studentName,
            quizName: attempt.quizName,
            course: attempt.courseName,
            score: Math.round((parseFloat(attempt.score || '0') / parseFloat(attempt.totalPoints || '1')) * 100),
            maxScore: 100,
            submittedAt: new Date(attempt.completedAt).toISOString().split('T')[0],
            status: attempt.isReviewed ? 'reviewed' : (attempt.passed ? 'passed' : 'needs_review'),
            attemptData: attempt
          })));
        }
      }
    } catch (error) {
      console.error('Error fetching quiz attempts:', error);
    } finally {
      setLoadingQuizzes(false);
    }
  };

  useEffect(() => {
    if (reportsData) {
      fetchQuizAttempts();
    }
  }, [reportsData, selectedCourse]);

  const handleReviewQuiz = async (attempt: any) => {
    try {
      const response = await fetch(`/api/quiz-attempts/${attempt.id}/review?teacherId=${teacherId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSelectedQuizAttempt(result.data);
          setReviewDialogOpen(true);
        }
      }
    } catch (error) {
      console.error('Error fetching quiz attempt details:', error);
    }
  };

  const submitReview = async () => {
    if (!selectedQuizAttempt) return;

    try {
      const response = await fetch(`/api/quiz-attempts/${selectedQuizAttempt.id}/review`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teacherId,
          reviewedAnswers: selectedQuizAttempt.answers,
          teacherFeedback: reviewFeedback,
          finalScore: selectedQuizAttempt.score,
          finalTotalPoints: selectedQuizAttempt.totalPoints,
          passed: selectedQuizAttempt.passed
        }),
      });

      if (response.ok) {
        setReviewDialogOpen(false);
        setSelectedQuizAttempt(null);
        setReviewFeedback('');
        fetchQuizAttempts(); // Refresh the list
      }
    } catch (error) {
      console.error('Error submitting review:', error);
    }
  };

  const certificates = [
    {
      id: 1,
      studentName: 'Carol Brown',
      course: 'Chemistry Basics',
      completedAt: '2024-07-30',
      certificateId: 'CERT-2024-001',
      status: 'issued'
    },
    {
      id: 2,
      studentName: 'Alice Johnson',
      course: 'Introduction to Algebra',
      completedAt: '2024-08-01',
      certificateId: 'CERT-2024-002',
      status: 'pending_verification'
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      on_track: 'default',
      behind: 'destructive',
      completed: 'secondary',
      passed: 'default',
      needs_review: 'destructive',
      issued: 'default',
      pending_verification: 'outline'
    };
    return (
      <Badge variant={variants[status] || 'outline'}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Reports & Analytics
          </h1>
          <p className='text-muted-foreground'>
            Track student progress and course performance
          </p>
        </div>
        <div className='flex space-x-2'>
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className='w-48'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {courses.map((course) => (
                <SelectItem key={course.id} value={course.id}>
                  {course.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant='outline'>
            <Download className='mr-2 h-4 w-4' />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Students
            </CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{reportsData.summary.totalStudents}</div>
            <p className='text-muted-foreground text-xs'>Across all courses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Average Progress
            </CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{Math.round(reportsData.summary.averageProgress)}%</div>
            <p className='text-muted-foreground text-xs'>Overall student progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Needs Review
            </CardTitle>
            <AlertCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{reportsData.summary.needsReview}</div>
            <p className='text-muted-foreground text-xs'>Quiz submissions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Active Courses
            </CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{reportsData.summary.totalCourses}</div>
            <p className='text-muted-foreground text-xs'>Currently teaching</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='progress' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='progress'>Student Progress</TabsTrigger>
          <TabsTrigger value='quizzes'>Quiz Results</TabsTrigger>
          <TabsTrigger value='certificates'>Certificates</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value='progress'>
          <Card>
            <CardHeader>
              <CardTitle>Student Progress Overview</CardTitle>
              <CardDescription>
                Track individual student progress across courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Quiz Average</TableHead>
                      <TableHead>Last Activity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {studentProgress.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{student.name}</p>
                            <p className='text-muted-foreground text-sm'>
                              {student.email}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>{student.course}</TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <Progress
                              value={student.overallProgress}
                              className='h-2'
                            />
                            <span className='text-sm'>
                              {student.overallProgress}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{student.quizAverage}%</TableCell>
                        <TableCell>
                          {new Date(student.lastActivity).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{getStatusBadge(student.status)}</TableCell>
                        <TableCell>
                          <Button variant='ghost' size='sm'>
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='quizzes'>
          <Card>
            <CardHeader>
              <CardTitle>Quiz Results</CardTitle>
              <CardDescription>
                Review and validate quiz submissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Quiz</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {quizResults.map((result) => (
                      <TableRow key={result.id}>
                        <TableCell>{result.studentName}</TableCell>
                        <TableCell>{result.quizName}</TableCell>
                        <TableCell>{result.course}</TableCell>
                        <TableCell>
                          <span className='font-medium'>
                            {result.score}/{result.maxScore}
                          </span>
                          <span className='text-muted-foreground ml-2 text-sm'>
                            (
                            {Math.round((result.score / result.maxScore) * 100)}
                            %)
                          </span>
                        </TableCell>
                        <TableCell>
                          {new Date(result.submittedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{getStatusBadge(result.status)}</TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleReviewQuiz(result.attemptData)}
                            >
                              <Eye className='h-4 w-4' />
                            </Button>
                            {result.status === 'needs_review' && (
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => handleReviewQuiz(result.attemptData)}
                              >
                                <Edit className='h-4 w-4' />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='certificates'>
          <Card>
            <CardHeader>
              <CardTitle>Certificate Management</CardTitle>
              <CardDescription>
                Manage and validate course completion certificates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Completed</TableHead>
                      <TableHead>Certificate ID</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[100px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {certificates.map((cert) => (
                      <TableRow key={cert.id}>
                        <TableCell>{cert.studentName}</TableCell>
                        <TableCell>{cert.course}</TableCell>
                        <TableCell>
                          {new Date(cert.completedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <code className='bg-muted rounded px-1 text-sm'>
                            {cert.certificateId}
                          </code>
                        </TableCell>
                        <TableCell>{getStatusBadge(cert.status)}</TableCell>
                        <TableCell>
                          <div className='flex space-x-1'>
                            <Button variant='ghost' size='sm'>
                              <Eye className='h-3 w-3' />
                            </Button>
                            <Button variant='ghost' size='sm'>
                              <Download className='h-3 w-3' />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='analytics'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Module Progress</CardTitle>
                <CardDescription>
                  Student progress across course modules
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <BarChart data={progressData}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='name' />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey='completed' fill='#22c55e' name='Completed' />
                    <Bar
                      dataKey='inProgress'
                      fill='#f59e0b'
                      name='In Progress'
                    />
                    <Bar
                      dataKey='notStarted'
                      fill='#ef4444'
                      name='Not Started'
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Course Completion</CardTitle>
                <CardDescription>
                  Overall course completion distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <PieChart>
                    <Pie
                      data={completionData}
                      cx='50%'
                      cy='50%'
                      outerRadius={80}
                      dataKey='value'
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {completionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Quiz Review Dialog */}
      <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Review Quiz Submission</DialogTitle>
            <DialogDescription>
              Review and provide feedback for this quiz attempt
            </DialogDescription>
          </DialogHeader>

          {selectedQuizAttempt && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold">Student Information</h4>
                  <p>Name: {selectedQuizAttempt.studentName}</p>
                  <p>Quiz: {selectedQuizAttempt.quizName}</p>
                  <p>Submitted: {new Date(selectedQuizAttempt.completedAt).toLocaleString()}</p>
                </div>
                <div>
                  <h4 className="font-semibold">Current Score</h4>
                  <p>Score: {selectedQuizAttempt.score}/{selectedQuizAttempt.totalPoints}</p>
                  <p>Percentage: {Math.round((parseFloat(selectedQuizAttempt.score) / parseFloat(selectedQuizAttempt.totalPoints)) * 100)}%</p>
                  <p>Status: {selectedQuizAttempt.passed ? 'Passed' : 'Failed'}</p>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Student Answers</h4>
                <div className="space-y-4">
                  {selectedQuizAttempt.answers?.map((answer: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="mb-2">
                        <h5 className="font-medium">Question {index + 1}</h5>
                        <p className="text-sm text-gray-600">{answer.questionText}</p>
                      </div>

                      <div className="mb-2">
                        <p className="text-sm">
                          <span className="font-medium">Student Answer:</span> {
                            typeof answer.studentAnswer === 'string'
                              ? answer.studentAnswer
                              : JSON.stringify(answer.studentAnswer)
                          }
                        </p>
                        {answer.questionType !== 'essay' && (
                          <p className="text-sm">
                            <span className="font-medium">Correct Answer:</span> {
                              typeof answer.correctAnswer === 'string'
                                ? answer.correctAnswer
                                : JSON.stringify(answer.correctAnswer)
                            }
                          </p>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge variant={answer.isCorrect ? 'default' : 'destructive'}>
                          {answer.isCorrect ? 'Correct' : 'Incorrect'}
                        </Badge>
                        <span className="text-sm">
                          Points: {answer.earnedPoints}/{answer.maxPoints}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Teacher Feedback</h4>
                <Textarea
                  placeholder="Provide feedback for the student..."
                  value={reviewFeedback}
                  onChange={(e) => setReviewFeedback(e.target.value)}
                  rows={4}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setReviewDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={submitReview}>
                  <Save className="w-4 h-4 mr-2" />
                  Submit Review
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
