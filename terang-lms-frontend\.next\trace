[{"name": "generate-buildid", "duration": 226, "timestamp": 2456901279, "id": 4, "parentId": 1, "tags": {}, "startTime": 1757666946285, "traceId": "d5499c57876ff7a5"}, {"name": "load-custom-routes", "duration": 2278, "timestamp": 2456901610, "id": 5, "parentId": 1, "tags": {}, "startTime": 1757666946286, "traceId": "d5499c57876ff7a5"}, {"name": "create-dist-dir", "duration": 56548, "timestamp": 2457019712, "id": 6, "parentId": 1, "tags": {}, "startTime": 1757666946404, "traceId": "d5499c57876ff7a5"}, {"name": "create-pages-mapping", "duration": 172, "timestamp": 2457081608, "id": 7, "parentId": 1, "tags": {}, "startTime": 1757666946466, "traceId": "d5499c57876ff7a5"}, {"name": "collect-app-paths", "duration": 4116, "timestamp": 2457081821, "id": 8, "parentId": 1, "tags": {}, "startTime": 1757666946466, "traceId": "d5499c57876ff7a5"}, {"name": "create-app-mapping", "duration": 4815, "timestamp": 2457085962, "id": 9, "parentId": 1, "tags": {}, "startTime": 1757666946470, "traceId": "d5499c57876ff7a5"}, {"name": "public-dir-conflict-check", "duration": 1164, "timestamp": 2457091957, "id": 10, "parentId": 1, "tags": {}, "startTime": 1757666946476, "traceId": "d5499c57876ff7a5"}, {"name": "generate-routes-manifest", "duration": 530, "timestamp": 2457093365, "id": 11, "parentId": 1, "tags": {}, "startTime": 1757666946477, "traceId": "d5499c57876ff7a5"}, {"name": "next-build", "duration": 1042306, "timestamp": 2456051610, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.2"}, "startTime": 1757666945436, "traceId": "d5499c57876ff7a5"}]