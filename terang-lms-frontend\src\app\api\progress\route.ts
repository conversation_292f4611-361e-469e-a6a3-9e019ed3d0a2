import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { 
  studentProgress, 
  studentContentProgress, 
  studentEnrollments,
  users,
  courses,
  modules,
  chapters
} from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';

// GET /api/progress - Get student progress for a course
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');
    const courseId = searchParams.get('courseId');
    
    if (!studentId || !courseId) {
      return NextResponse.json(
        { error: 'Student ID and Course ID are required' }, 
        { status: 400 }
      );
    }

    // Get overall course progress
    const courseProgress = await db
      .select({
        id: studentProgress.id,
        courseId: studentProgress.courseId,
        moduleId: studentProgress.moduleId,
        chapterId: studentProgress.chapterId,
        completed: studentProgress.completed,
        completedAt: studentProgress.completedAt,
        progressPercentage: studentProgress.progressPercentage,
        timeSpent: studentProgress.timeSpent,
        lastAccessedAt: studentProgress.lastAccessedAt,
        moduleName: modules.name,
        chapterName: chapters.name
      })
      .from(studentProgress)
      .leftJoin(modules, eq(studentProgress.moduleId, modules.id))
      .leftJoin(chapters, eq(studentProgress.chapterId, chapters.id))
      .where(
        and(
          eq(studentProgress.studentId, parseInt(studentId)),
          eq(studentProgress.courseId, parseInt(courseId))
        )
      )
      .orderBy(desc(studentProgress.lastAccessedAt));

    // Get content progress
    const contentProgress = await db
      .select({
        id: studentContentProgress.id,
        chapterId: studentContentProgress.chapterId,
        contentId: studentContentProgress.contentId,
        completed: studentContentProgress.completed,
        completedAt: studentContentProgress.completedAt,
        timeSpent: studentContentProgress.timeSpent,
        chapterName: chapters.name
      })
      .from(studentContentProgress)
      .leftJoin(chapters, eq(studentContentProgress.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .where(
        and(
          eq(studentContentProgress.studentId, parseInt(studentId)),
          eq(modules.courseId, parseInt(courseId))
        )
      );

    // Get enrollment info
    const enrollment = await db
      .select({
        enrolledAt: studentEnrollments.enrolledAt,
        completedAt: studentEnrollments.completedAt,
        finalScore: studentEnrollments.finalScore,
        certificateGenerated: studentEnrollments.certificateGenerated
      })
      .from(studentEnrollments)
      .where(
        and(
          eq(studentEnrollments.studentId, parseInt(studentId)),
          eq(studentEnrollments.courseId, parseInt(courseId))
        )
      )
      .limit(1);

    return NextResponse.json({
      success: true,
      data: {
        courseProgress,
        contentProgress,
        enrollment: enrollment[0] || null
      }
    });

  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/progress - Update student progress
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      studentId, 
      courseId, 
      moduleId, 
      chapterId, 
      contentId,
      completed = false,
      progressPercentage = 0,
      timeSpent = 0,
      type = 'chapter' // 'chapter', 'module', 'content'
    } = body;

    if (!studentId || !courseId) {
      return NextResponse.json(
        { error: 'Student ID and Course ID are required' }, 
        { status: 400 }
      );
    }

    if (type === 'content' && contentId && chapterId) {
      // Update content progress
      const existingContentProgress = await db
        .select()
        .from(studentContentProgress)
        .where(
          and(
            eq(studentContentProgress.studentId, studentId),
            eq(studentContentProgress.chapterId, chapterId),
            eq(studentContentProgress.contentId, contentId)
          )
        )
        .limit(1);

      if (existingContentProgress.length > 0) {
        // Update existing content progress
        await db
          .update(studentContentProgress)
          .set({
            completed,
            completedAt: completed ? new Date() : null,
            timeSpent: existingContentProgress[0].timeSpent + timeSpent,
            updatedAt: new Date()
          })
          .where(eq(studentContentProgress.id, existingContentProgress[0].id));
      } else {
        // Create new content progress
        await db
          .insert(studentContentProgress)
          .values({
            studentId,
            chapterId,
            contentId,
            completed,
            completedAt: completed ? new Date() : null,
            timeSpent
          });
      }
    }

    if (type === 'chapter' || type === 'module') {
      // Update chapter/module progress
      const existingProgress = await db
        .select()
        .from(studentProgress)
        .where(
          and(
            eq(studentProgress.studentId, studentId),
            eq(studentProgress.courseId, courseId),
            type === 'chapter' ? eq(studentProgress.chapterId, chapterId) : eq(studentProgress.moduleId, moduleId)
          )
        )
        .limit(1);

      if (existingProgress.length > 0) {
        // Update existing progress
        await db
          .update(studentProgress)
          .set({
            completed,
            completedAt: completed ? new Date() : null,
            progressPercentage,
            timeSpent: existingProgress[0].timeSpent + timeSpent,
            lastAccessedAt: new Date(),
            updatedAt: new Date()
          })
          .where(eq(studentProgress.id, existingProgress[0].id));
      } else {
        // Create new progress
        await db
          .insert(studentProgress)
          .values({
            studentId,
            courseId,
            moduleId: type === 'module' ? moduleId : null,
            chapterId: type === 'chapter' ? chapterId : null,
            completed,
            completedAt: completed ? new Date() : null,
            progressPercentage,
            timeSpent,
            lastAccessedAt: new Date()
          });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Progress updated successfully'
    });

  } catch (error) {
    console.error('Error updating progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
