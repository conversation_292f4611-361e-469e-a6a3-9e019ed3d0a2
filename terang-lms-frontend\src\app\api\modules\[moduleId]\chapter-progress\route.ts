import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { studentProgress, chapters, modules } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { moduleId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    const moduleId = parseInt(params.moduleId);
    
    // Get module info with course
    const moduleInfo = await db
      .select({
        moduleId: modules.id,
        courseId: modules.courseId,
        totalChapters: modules.chapterCount
      })
      .from(modules)
      .where(eq(modules.id, moduleId))
      .limit(1);

    if (moduleInfo.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Module not found' },
        { status: 404 }
      );
    }

    const module = moduleInfo[0];

    // Get all chapters in this module
    const moduleChapters = await db
      .select({ id: chapters.id })
      .from(chapters)
      .where(eq(chapters.moduleId, moduleId));

    const totalChapters = moduleChapters.length;

    // Get completed chapters for this student in this module
    const completedChapters = await db
      .select()
      .from(studentProgress)
      .innerJoin(chapters, eq(studentProgress.chapterId, chapters.id))
      .where(
        and(
          eq(studentProgress.studentId, parseInt(studentId)),
          eq(chapters.moduleId, moduleId),
          eq(studentProgress.completed, true)
        )
      );

    const completedCount = completedChapters.length;
    const allChaptersCompleted = totalChapters > 0 && completedCount >= totalChapters;

    // Calculate average score from completed chapters
    const averageScore = completedChapters.length > 0
      ? completedChapters.reduce((sum, chapter) => sum + (chapter.studentProgress.progressPercentage || 0), 0) / completedChapters.length
      : 0;

    return NextResponse.json({
      success: true,
      data: {
        moduleId: module.moduleId,
        courseId: module.courseId,
        completedChapters: completedCount,
        totalChapters,
        allChaptersCompleted,
        progressPercentage: totalChapters > 0 ? Math.round((completedCount / totalChapters) * 100) : 0,
        averageScore: Math.round(averageScore)
      }
    });

  } catch (error) {
    console.error('Error checking module chapter progress:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
