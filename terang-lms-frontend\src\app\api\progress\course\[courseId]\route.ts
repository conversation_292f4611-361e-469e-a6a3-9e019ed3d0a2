import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { 
  studentProgress, 
  studentContentProgress,
  studentEnrollments,
  quizAttempts,
  users,
  courses,
  modules,
  chapters,
  quizzes
} from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';

// GET /api/progress/course/[courseId] - Get detailed progress for a specific course
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ courseId: string }> }
) {
  try {
    const { courseId } = await params;
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' }, 
        { status: 400 }
      );
    }

    const courseIdInt = parseInt(courseId);
    if (isNaN(courseIdInt)) {
      return NextResponse.json(
        { error: 'Invalid course ID' }, 
        { status: 400 }
      );
    }

    // Get course details
    const course = await db
      .select()
      .from(courses)
      .where(eq(courses.id, courseIdInt))
      .limit(1);

    if (course.length === 0) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    // Get enrollment info
    const enrollment = await db
      .select()
      .from(studentEnrollments)
      .where(
        and(
          eq(studentEnrollments.studentId, parseInt(studentId)),
          eq(studentEnrollments.courseId, courseIdInt)
        )
      )
      .limit(1);

    if (enrollment.length === 0) {
      return NextResponse.json(
        { error: 'Student not enrolled in this course' },
        { status: 403 }
      );
    }

    // Get all modules for this course
    const courseModules = await db
      .select()
      .from(modules)
      .where(eq(modules.courseId, courseIdInt))
      .orderBy(modules.orderIndex);

    // Get all chapters for this course
    const courseChapters = await db
      .select({
        id: chapters.id,
        name: chapters.name,
        content: chapters.content,
        moduleId: chapters.moduleId,
        orderIndex: chapters.orderIndex
      })
      .from(chapters)
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .where(eq(modules.courseId, courseIdInt))
      .orderBy(chapters.orderIndex);

    // Get student progress for modules
    const moduleProgress = await db
      .select()
      .from(studentProgress)
      .where(
        and(
          eq(studentProgress.studentId, parseInt(studentId)),
          eq(studentProgress.courseId, courseIdInt),
          eq(studentProgress.moduleId, modules.id)
        )
      )
      .leftJoin(modules, eq(studentProgress.moduleId, modules.id));

    // Get student progress for chapters
    const chapterProgress = await db
      .select()
      .from(studentProgress)
      .where(
        and(
          eq(studentProgress.studentId, parseInt(studentId)),
          eq(studentProgress.courseId, courseIdInt),
          eq(studentProgress.chapterId, chapters.id)
        )
      )
      .leftJoin(chapters, eq(studentProgress.chapterId, chapters.id));

    // Get content progress
    const contentProgress = await db
      .select()
      .from(studentContentProgress)
      .leftJoin(chapters, eq(studentContentProgress.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .where(
        and(
          eq(studentContentProgress.studentId, parseInt(studentId)),
          eq(modules.courseId, courseIdInt)
        )
      );

    // Get quiz attempts for this course
    const courseQuizAttempts = await db
      .select({
        id: quizAttempts.id,
        quizId: quizAttempts.quizId,
        score: quizAttempts.score,
        totalPoints: quizAttempts.totalPoints,
        passed: quizAttempts.passed,
        completedAt: quizAttempts.completedAt,
        attemptNumber: quizAttempts.attemptNumber,
        isReviewed: quizAttempts.isReviewed,
        answers: quizAttempts.answers,
        quizName: quizzes.name,
        quizType: quizzes.quizType,
        chapterId: quizzes.chapterId,
        moduleId: quizzes.moduleId
      })
      .from(quizAttempts)
      .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
      .where(
        and(
          eq(quizAttempts.studentId, parseInt(studentId)),
          eq(quizzes.courseId, courseIdInt)
        )
      )
      .orderBy(desc(quizAttempts.completedAt));

    // Build structured progress data
    const structuredModules = courseModules.map(module => {
      const moduleProgressData = moduleProgress.find(mp => mp.student_progress?.moduleId === module.id);
      const moduleChapters = courseChapters.filter(ch => ch.moduleId === module.id);
      
      const chaptersWithProgress = moduleChapters.map(chapter => {
        const chapterProgressData = chapterProgress.find(cp => cp.student_progress?.chapterId === chapter.id);
        const chapterContentProgress = contentProgress.filter(cp => cp.student_content_progress?.chapterId === chapter.id);
        const chapterQuizzes = courseQuizAttempts.filter(qa => qa.chapterId === chapter.id);
        
        // Parse content from chapter JSON
        const content = chapter.content as any;
        const contentItems = content?.contents || [];
        
        const contentWithProgress = contentItems.map((item: any) => {
          const itemProgress = chapterContentProgress.find(cp => cp.student_content_progress?.contentId === item.id);
          return {
            ...item,
            isCompleted: itemProgress?.student_content_progress?.completed || false,
            timeSpent: itemProgress?.student_content_progress?.timeSpent || 0
          };
        });

        return {
          id: chapter.id,
          name: chapter.name,
          orderIndex: chapter.orderIndex,
          isCompleted: chapterProgressData?.student_progress?.completed || false,
          progressPercentage: parseFloat(chapterProgressData?.student_progress?.progressPercentage || '0'),
          timeSpent: chapterProgressData?.student_progress?.timeSpent || 0,
          lastAccessedAt: chapterProgressData?.student_progress?.lastAccessedAt,
          contents: contentWithProgress,
          quizzes: chapterQuizzes
        };
      });

      const moduleQuizzes = courseQuizAttempts.filter(qa => qa.moduleId === module.id);
      
      return {
        id: module.id,
        name: module.name,
        description: module.description,
        orderIndex: module.orderIndex,
        isCompleted: moduleProgressData?.student_progress?.completed || false,
        progressPercentage: parseFloat(moduleProgressData?.student_progress?.progressPercentage || '0'),
        timeSpent: moduleProgressData?.student_progress?.timeSpent || 0,
        lastAccessedAt: moduleProgressData?.student_progress?.lastAccessedAt,
        chapters: chaptersWithProgress,
        quizzes: moduleQuizzes
      };
    });

    // Calculate overall progress
    const totalChapters = courseChapters.length;
    const completedChapters = chapterProgress.filter(cp => cp.student_progress?.completed).length;
    const overallProgress = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

    const totalModules = courseModules.length;
    const completedModules = moduleProgress.filter(mp => mp.student_progress?.completed).length;

    return NextResponse.json({
      success: true,
      data: {
        course: course[0],
        enrollment: enrollment[0],
        modules: structuredModules,
        overallProgress: Math.round(overallProgress),
        stats: {
          totalModules,
          completedModules,
          totalChapters,
          completedChapters,
          totalQuizzes: courseQuizAttempts.length,
          passedQuizzes: courseQuizAttempts.filter(qa => qa.passed).length,
          averageQuizScore: courseQuizAttempts.length > 0 
            ? courseQuizAttempts.reduce((sum, qa) => {
                const score = parseFloat(qa.score || '0');
                const total = parseFloat(qa.totalPoints || '1');
                return sum + ((score / total) * 100);
              }, 0) / courseQuizAttempts.length
            : 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching course progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
