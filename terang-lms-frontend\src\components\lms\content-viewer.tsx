/**
 * Content Viewer Component with Progress Tracking
 * Example implementation showing how to integrate progress tracking
 */

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Clock, BookOpen } from 'lucide-react';
import { useProgressTracker } from '@/hooks/useProgressTracker';

interface ContentViewerProps {
  contentId: string;
  chapterId: number;
  moduleId: number;
  courseId: number;
  title: string;
  content: string;
  estimatedDuration?: number; // in minutes
  onComplete?: () => void;
}

export const ContentViewer: React.FC<ContentViewerProps> = ({
  contentId,
  chapterId,
  moduleId,
  courseId,
  title,
  content,
  estimatedDuration = 5,
  onComplete
}) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [hasStartedReading, setHasStartedReading] = useState(false);

  const {
    startTimeTracking,
    stopTimeTracking,
    markContentCompleted,
    updateProgress,
    getCurrentTimeSpent,
    isTracking
  } = useProgressTracker();

  // Start tracking when component mounts
  useEffect(() => {
    if (!hasStartedReading) {
      startTimeTracking({
        contentId,
        chapterId
      });
      setHasStartedReading(true);
    }

    return () => {
      // Stop tracking when component unmounts
      stopTimeTracking();
    };
  }, [contentId, chapterId, hasStartedReading, startTimeTracking, stopTimeTracking]);

  // Track reading progress based on scroll
  useEffect(() => {
    const handleScroll = () => {
      const element = document.getElementById('content-body');
      if (element) {
        const scrollTop = element.scrollTop;
        const scrollHeight = element.scrollHeight - element.clientHeight;
        const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
        
        setReadingProgress(Math.min(progress, 100));
        
        // Update progress every 25% increment
        if (progress >= 25 && progress < 50 && readingProgress < 25) {
          updateProgress('content', contentId, 25, { chapterId });
        } else if (progress >= 50 && progress < 75 && readingProgress < 50) {
          updateProgress('content', contentId, 50, { chapterId });
        } else if (progress >= 75 && progress < 100 && readingProgress < 75) {
          updateProgress('content', contentId, 75, { chapterId });
        }
      }
    };

    const contentElement = document.getElementById('content-body');
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll);
      return () => contentElement.removeEventListener('scroll', handleScroll);
    }
  }, [contentId, chapterId, readingProgress, updateProgress]);

  const handleMarkAsCompleted = () => {
    markContentCompleted(contentId, chapterId);
    setIsCompleted(true);
    stopTimeTracking(true);
    onComplete?.();
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-xl">{title}</CardTitle>
            </div>
            <div className="flex items-center space-x-4">
              {isTracking && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>Time: {formatTime(getCurrentTimeSpent())}</span>
                </div>
              )}
              <div className="text-sm text-gray-600">
                Est. {estimatedDuration} min
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Indicator */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Reading Progress</span>
              <span>{Math.round(readingProgress)}%</span>
            </div>
            <Progress value={readingProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      <Card>
        <CardContent className="pt-6">
          <div
            id="content-body"
            className="prose max-w-none overflow-y-auto max-h-96 p-4 border rounded-lg"
            style={{ scrollBehavior: 'smooth' }}
          >
            <div dangerouslySetInnerHTML={{ __html: content }} />
          </div>
        </CardContent>
      </Card>

      {/* Completion Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isCompleted ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-600 font-medium">Completed</span>
                </>
              ) : (
                <span className="text-gray-600">
                  {readingProgress >= 80 
                    ? "Almost done! Mark as completed when ready." 
                    : "Continue reading to track your progress."
                  }
                </span>
              )}
            </div>
            
            <div className="space-x-2">
              {!isCompleted && readingProgress >= 80 && (
                <Button onClick={handleMarkAsCompleted}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Completed
                </Button>
              )}
              {isCompleted && (
                <Button variant="outline" disabled>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Completed
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle className="text-sm">Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs space-y-1 text-gray-600">
              <div>Content ID: {contentId}</div>
              <div>Chapter ID: {chapterId}</div>
              <div>Module ID: {moduleId}</div>
              <div>Course ID: {courseId}</div>
              <div>Is Tracking: {isTracking ? 'Yes' : 'No'}</div>
              <div>Time Spent: {formatTime(getCurrentTimeSpent())}</div>
              <div>Reading Progress: {Math.round(readingProgress)}%</div>
              <div>Is Completed: {isCompleted ? 'Yes' : 'No'}</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ContentViewer;
