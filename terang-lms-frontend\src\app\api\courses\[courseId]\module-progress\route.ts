import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { studentProgress, modules, courses } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    const courseId = parseInt(params.courseId);
    
    // Get course info
    const courseInfo = await db
      .select({
        courseId: courses.id,
        courseName: courses.name,
        totalModules: courses.moduleCount
      })
      .from(courses)
      .where(eq(courses.id, courseId))
      .limit(1);

    if (courseInfo.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      );
    }

    const course = courseInfo[0];

    // Get all modules in this course
    const courseModules = await db
      .select({ id: modules.id })
      .from(modules)
      .where(eq(modules.courseId, courseId));

    const totalModules = courseModules.length;

    // Get completed modules for this student in this course
    const completedModules = await db
      .select()
      .from(studentProgress)
      .where(
        and(
          eq(studentProgress.studentId, parseInt(studentId)),
          eq(studentProgress.courseId, courseId),
          eq(studentProgress.moduleId, modules.id),
          eq(studentProgress.completed, true)
        )
      );

    const completedCount = completedModules.length;
    const allModulesCompleted = totalModules > 0 && completedCount >= totalModules;

    // Calculate average score from completed modules
    const averageScore = completedModules.length > 0
      ? completedModules.reduce((sum, module) => sum + (module.progressPercentage || 0), 0) / completedModules.length
      : 0;

    return NextResponse.json({
      success: true,
      data: {
        courseId: course.courseId,
        courseName: course.courseName,
        completedModules: completedCount,
        totalModules,
        allModulesCompleted,
        progressPercentage: totalModules > 0 ? Math.round((completedCount / totalModules) * 100) : 0,
        averageScore: Math.round(averageScore)
      }
    });

  } catch (error) {
    console.error('Error checking course module progress:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
