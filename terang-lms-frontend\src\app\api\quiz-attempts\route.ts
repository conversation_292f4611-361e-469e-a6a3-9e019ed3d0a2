import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { 
  quizAttempts, 
  quizzes, 
  questions,
  users,
  courses,
  modules,
  chapters
} from '@/lib/db/schema';
import { eq, and, desc, count } from 'drizzle-orm';

// GET /api/quiz-attempts - Get quiz attempts for a student or quiz
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');
    const quizId = searchParams.get('quizId');
    const courseId = searchParams.get('courseId');
    
    if (!studentId && !quizId) {
      return NextResponse.json(
        { error: 'Either Student ID or Quiz ID is required' }, 
        { status: 400 }
      );
    }

    let whereConditions = [];
    
    if (studentId) {
      whereConditions.push(eq(quizAttempts.studentId, parseInt(studentId)));
    }
    
    if (quizId) {
      whereConditions.push(eq(quizAttempts.quizId, parseInt(quizId)));
    }

    const attempts = await db
      .select({
        id: quizAttempts.id,
        studentId: quizAttempts.studentId,
        quizId: quizAttempts.quizId,
        score: quizAttempts.score,
        totalPoints: quizAttempts.totalPoints,
        passed: quizAttempts.passed,
        startedAt: quizAttempts.startedAt,
        completedAt: quizAttempts.completedAt,
        answers: quizAttempts.answers,
        timeSpent: quizAttempts.timeSpent,
        attemptNumber: quizAttempts.attemptNumber,
        isReviewed: quizAttempts.isReviewed,
        teacherFeedback: quizAttempts.teacherFeedback,
        studentName: users.name,
        studentEmail: users.email,
        quizName: quizzes.name,
        quizType: quizzes.quizType,
        minimumScore: quizzes.minimumScore,
        courseName: courses.name,
        moduleName: modules.name,
        chapterName: chapters.name
      })
      .from(quizAttempts)
      .leftJoin(users, eq(quizAttempts.studentId, users.id))
      .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
      .leftJoin(courses, eq(quizzes.courseId, courses.id))
      .leftJoin(modules, eq(quizzes.moduleId, modules.id))
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .where(and(...whereConditions))
      .orderBy(desc(quizAttempts.startedAt));

    // If courseId is provided, filter by course
    let filteredAttempts = attempts;
    if (courseId) {
      filteredAttempts = attempts.filter(attempt => 
        attempt.courseName && courses.id === parseInt(courseId)
      );
    }

    return NextResponse.json({
      success: true,
      data: filteredAttempts
    });

  } catch (error) {
    console.error('Error fetching quiz attempts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/quiz-attempts - Submit a quiz attempt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      studentId, 
      quizId, 
      answers, 
      startedAt,
      timeSpent = 0
    } = body;

    if (!studentId || !quizId || !answers) {
      return NextResponse.json(
        { error: 'Student ID, Quiz ID, and answers are required' }, 
        { status: 400 }
      );
    }

    // Get quiz details and questions
    const quiz = await db
      .select()
      .from(quizzes)
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (quiz.length === 0) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    const quizQuestions = await db
      .select()
      .from(questions)
      .where(eq(questions.quizId, quizId));

    // Calculate score
    let totalScore = 0;
    let totalPoints = 0;
    const detailedAnswers = [];

    for (const question of quizQuestions) {
      totalPoints += parseFloat(question.points?.toString() || '1');
      
      const studentAnswer = answers.find((a: any) => a.questionId === question.id);
      let isCorrect = false;
      let earnedPoints = 0;

      if (studentAnswer) {
        if (question.type === 'multiple_choice') {
          const options = question.options as any;
          if (options && options.choices) {
            const correctIndex = options.choices.findIndex((choice: any) => choice.correct_answer === true);
            isCorrect = studentAnswer.selectedOption === correctIndex;
          }
        } else if (question.type === 'true_false') {
          const options = question.options as any;
          isCorrect = studentAnswer.answer === options?.correct_answer;
        } else if (question.type === 'essay') {
          // Essay questions need manual review
          isCorrect = false; // Will be reviewed by teacher
        }

        if (isCorrect) {
          earnedPoints = parseFloat(question.points?.toString() || '1');
          totalScore += earnedPoints;
        }
      }

      detailedAnswers.push({
        questionId: question.id,
        questionText: question.question,
        questionType: question.type,
        studentAnswer: studentAnswer?.answer || studentAnswer?.selectedOption || null,
        correctAnswer: question.type === 'essay' ? null : (question.options as any)?.correct_answer,
        isCorrect,
        earnedPoints,
        maxPoints: parseFloat(question.points?.toString() || '1'),
        explanation: question.explanation
      });
    }

    const percentage = totalPoints > 0 ? (totalScore / totalPoints) * 100 : 0;
    const passed = percentage >= parseFloat(quiz[0].minimumScore?.toString() || '70');

    // Get attempt number
    const existingAttempts = await db
      .select({ count: count() })
      .from(quizAttempts)
      .where(
        and(
          eq(quizAttempts.studentId, studentId),
          eq(quizAttempts.quizId, quizId)
        )
      );

    const attemptNumber = (existingAttempts[0]?.count || 0) + 1;

    // Insert quiz attempt
    const result = await db
      .insert(quizAttempts)
      .values({
        studentId,
        quizId,
        score: totalScore.toString(),
        totalPoints: totalPoints.toString(),
        passed,
        startedAt: new Date(startedAt),
        completedAt: new Date(),
        answers: detailedAnswers,
        timeSpent,
        attemptNumber,
        isReviewed: quiz[0].type === 'verified' ? false : true // Auto-review for self-paced
      })
      .returning();

    return NextResponse.json({
      success: true,
      data: {
        attemptId: result[0].id,
        score: totalScore,
        totalPoints,
        percentage: Math.round(percentage),
        passed,
        attemptNumber,
        needsReview: quiz[0].type === 'verified' && detailedAnswers.some(a => a.questionType === 'essay')
      }
    });

  } catch (error) {
    console.error('Error submitting quiz attempt:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
