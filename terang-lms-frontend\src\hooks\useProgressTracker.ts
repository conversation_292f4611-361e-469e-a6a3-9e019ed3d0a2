/**
 * React Hook for Progress Tracking
 * Provides easy-to-use functions for tracking student progress in React components
 */

import { useCallback, useEffect, useRef } from 'react';
import { 
  progressTracker, 
  ContentProgressData, 
  ChapterProgressData, 
  ModuleProgressData 
} from '@/lib/progress-tracker';

export interface UseProgressTrackerOptions {
  autoTrackTime?: boolean;
  timeTrackingInterval?: number; // in seconds
}

export interface TimeTrackingSession {
  contentId?: string;
  chapterId?: number;
  moduleId?: number;
  courseId?: number;
  startTime: number;
}

export const useProgressTracker = (options: UseProgressTrackerOptions = {}) => {
  const { autoTrackTime = true, timeTrackingInterval = 30 } = options;
  
  const timeTrackingRef = useRef<TimeTrackingSession | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Start tracking time for content/chapter/module
   */
  const startTimeTracking = useCallback((session: Omit<TimeTrackingSession, 'startTime'>) => {
    // Stop any existing tracking
    stopTimeTracking();
    
    timeTrackingRef.current = {
      ...session,
      startTime: Date.now()
    };

    if (autoTrackTime) {
      intervalRef.current = setInterval(() => {
        if (timeTrackingRef.current) {
          const timeSpent = Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000);
          
          // Update progress with time spent
          if (timeTrackingRef.current.contentId && timeTrackingRef.current.chapterId) {
            progressTracker.trackContentProgress({
              contentId: timeTrackingRef.current.contentId,
              chapterId: timeTrackingRef.current.chapterId,
              timeSpent,
              completed: false
            });
          } else if (timeTrackingRef.current.chapterId && timeTrackingRef.current.moduleId && timeTrackingRef.current.courseId) {
            progressTracker.trackChapterProgress({
              chapterId: timeTrackingRef.current.chapterId,
              moduleId: timeTrackingRef.current.moduleId,
              courseId: timeTrackingRef.current.courseId,
              timeSpent,
              completed: false,
              progressPercentage: 0 // Will be calculated on the backend
            });
          } else if (timeTrackingRef.current.moduleId && timeTrackingRef.current.courseId) {
            progressTracker.trackModuleProgress({
              moduleId: timeTrackingRef.current.moduleId,
              courseId: timeTrackingRef.current.courseId,
              timeSpent,
              completed: false,
              progressPercentage: 0 // Will be calculated on the backend
            });
          }
        }
      }, timeTrackingInterval * 1000);
    }
  }, [autoTrackTime, timeTrackingInterval]);

  /**
   * Stop time tracking and optionally mark as completed
   */
  const stopTimeTracking = useCallback((markCompleted = false) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (timeTrackingRef.current && markCompleted) {
      const timeSpent = Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000);
      
      // Mark as completed with final time
      if (timeTrackingRef.current.contentId && timeTrackingRef.current.chapterId) {
        progressTracker.trackContentProgress({
          contentId: timeTrackingRef.current.contentId,
          chapterId: timeTrackingRef.current.chapterId,
          timeSpent,
          completed: true
        });
      } else if (timeTrackingRef.current.chapterId && timeTrackingRef.current.moduleId && timeTrackingRef.current.courseId) {
        progressTracker.trackChapterProgress({
          chapterId: timeTrackingRef.current.chapterId,
          moduleId: timeTrackingRef.current.moduleId,
          courseId: timeTrackingRef.current.courseId,
          timeSpent,
          completed: true,
          progressPercentage: 100
        });
      } else if (timeTrackingRef.current.moduleId && timeTrackingRef.current.courseId) {
        progressTracker.trackModuleProgress({
          moduleId: timeTrackingRef.current.moduleId,
          courseId: timeTrackingRef.current.courseId,
          timeSpent,
          completed: true,
          progressPercentage: 100
        });
      }
    }

    timeTrackingRef.current = null;
  }, []);

  /**
   * Track content completion
   */
  const markContentCompleted = useCallback((contentId: string, chapterId: number) => {
    const timeSpent = timeTrackingRef.current?.contentId === contentId 
      ? Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000)
      : 0;

    progressTracker.trackContentProgress({
      contentId,
      chapterId,
      timeSpent,
      completed: true
    });
  }, []);

  /**
   * Track chapter completion
   */
  const markChapterCompleted = useCallback((chapterId: number, moduleId: number, courseId: number) => {
    const timeSpent = timeTrackingRef.current?.chapterId === chapterId 
      ? Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000)
      : 0;

    progressTracker.trackChapterProgress({
      chapterId,
      moduleId,
      courseId,
      timeSpent,
      completed: true,
      progressPercentage: 100
    });
  }, []);

  /**
   * Track module completion
   */
  const markModuleCompleted = useCallback((moduleId: number, courseId: number) => {
    const timeSpent = timeTrackingRef.current?.moduleId === moduleId 
      ? Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000)
      : 0;

    progressTracker.trackModuleProgress({
      moduleId,
      courseId,
      timeSpent,
      completed: true,
      progressPercentage: 100
    });
  }, []);

  /**
   * Track quiz completion
   */
  const markQuizCompleted = useCallback((
    quizId: number,
    passed: boolean,
    score: number,
    chapterId?: number,
    moduleId?: number,
    courseId?: number
  ) => {
    progressTracker.trackQuizCompletion(quizId, passed, score, chapterId, moduleId, courseId);
  }, []);

  /**
   * Update progress percentage for ongoing work
   */
  const updateProgress = useCallback((
    type: 'content' | 'chapter' | 'module',
    id: string | number,
    progressPercentage: number,
    additionalData?: Partial<ContentProgressData | ChapterProgressData | ModuleProgressData>
  ) => {
    const timeSpent = timeTrackingRef.current 
      ? Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000)
      : 0;

    if (type === 'content' && typeof id === 'string' && additionalData && 'chapterId' in additionalData) {
      progressTracker.trackContentProgress({
        contentId: id,
        chapterId: additionalData.chapterId!,
        timeSpent,
        completed: progressPercentage >= 100,
        ...additionalData
      });
    } else if (type === 'chapter' && typeof id === 'number' && additionalData && 'moduleId' in additionalData && 'courseId' in additionalData) {
      progressTracker.trackChapterProgress({
        chapterId: id,
        moduleId: additionalData.moduleId!,
        courseId: additionalData.courseId!,
        timeSpent,
        completed: progressPercentage >= 100,
        progressPercentage,
        ...additionalData
      });
    } else if (type === 'module' && typeof id === 'number' && additionalData && 'courseId' in additionalData) {
      progressTracker.trackModuleProgress({
        moduleId: id,
        courseId: additionalData.courseId!,
        timeSpent,
        completed: progressPercentage >= 100,
        progressPercentage,
        ...additionalData
      });
    }
  }, []);

  /**
   * Get current time spent in session
   */
  const getCurrentTimeSpent = useCallback(() => {
    if (!timeTrackingRef.current) return 0;
    return Math.floor((Date.now() - timeTrackingRef.current.startTime) / 1000);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTimeTracking();
    };
  }, [stopTimeTracking]);

  // Auto-stop tracking when page becomes hidden
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && timeTrackingRef.current) {
        // Pause tracking when page is hidden
        stopTimeTracking();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [stopTimeTracking]);

  return {
    startTimeTracking,
    stopTimeTracking,
    markContentCompleted,
    markChapterCompleted,
    markModuleCompleted,
    markQuizCompleted,
    updateProgress,
    getCurrentTimeSpent,
    isTracking: !!timeTrackingRef.current
  };
};

export default useProgressTracker;
