import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { studentContentProgress, chapters, modules } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { chapterId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    const chapterId = parseInt(params.chapterId);
    
    // Get chapter info with module and course
    const chapterInfo = await db
      .select({
        chapterId: chapters.id,
        moduleId: chapters.moduleId,
        courseId: modules.courseId,
        totalContent: chapters.contentCount
      })
      .from(chapters)
      .innerJoin(modules, eq(chapters.moduleId, modules.id))
      .where(eq(chapters.id, chapterId))
      .limit(1);

    if (chapterInfo.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Chapter not found' },
        { status: 404 }
      );
    }

    const chapter = chapterInfo[0];

    // Get completed content count for this chapter
    const completedContent = await db
      .select()
      .from(studentContentProgress)
      .where(
        and(
          eq(studentContentProgress.studentId, parseInt(studentId)),
          eq(studentContentProgress.chapterId, chapterId),
          eq(studentContentProgress.completed, true)
        )
      );

    const completedCount = completedContent.length;
    const totalCount = chapter.totalContent || 0;
    const allContentCompleted = totalCount > 0 && completedCount >= totalCount;

    return NextResponse.json({
      success: true,
      data: {
        chapterId: chapter.chapterId,
        moduleId: chapter.moduleId,
        courseId: chapter.courseId,
        completedContent: completedCount,
        totalContent: totalCount,
        allContentCompleted,
        progressPercentage: totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0
      }
    });

  } catch (error) {
    console.error('Error checking chapter content progress:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
